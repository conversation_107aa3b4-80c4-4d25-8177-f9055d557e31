import { UploadRequest } from './upload';
import { CompleteBody } from '../types';

export class CompleteRequest extends UploadRequest {

	constructor(bucket: R2Bucket, private readonly workflow: Workflow, key: string, uploadId: string, private readonly body: CompleteBody | null) {
		super(bucket, key, uploadId);
	}

	async execute(): Promise<Response> {
		if (this.body === null) {
			return this.error("Missing or incomplete body");
		}
		try {
			const object = await this.upload.complete(this.body.parts);
			this.workflow.create({ params: { r2Path: object.key, id: object.key } });
			return new Response(null, {
				headers: {
					etag: object.httpEtag
				}
			});
		} catch (error: any) {
			return this.error(error);
		}
	}
}
