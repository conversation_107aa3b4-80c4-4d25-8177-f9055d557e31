import { UploadRequest } from './upload';

export class UploadPartRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly partNumber: string, private readonly body: Request['body']) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    if (this.body === null) {
      return this.error("Missing request body");
    }
    const partNumber = parseInt(this.partNumber);
    if (isNaN(partNumber)) {
      return this.error("Invalid part number");
    }
    try {
      const part = await this.upload.uploadPart(partNumber, this.body);
      return this.json(part);
    } catch (error: any) {
      return this.error(error);
    }
  }
}
